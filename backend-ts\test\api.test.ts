/**
 * Comprehensive test suite for the TypeScript backend migration
 * Tests both legacy API and new LangGraph streaming API
 */

interface TestMessage {
  content: string;
  type: string;
}

interface TestConfig {
  configurable?: {
    initial_search_query_count?: number;
    max_research_loops?: number;
    reasoning_model?: string;
  };
}

/**
 * Test the legacy API endpoint for backward compatibility
 */
async function testLegacyApi() {
  console.log('\n🔍 Testing Legacy API Endpoint...');

  try {
    const apiUrl = 'http://localhost:2024/api/run';

    const testMessage = {
      messages: [
        {
          type: 'human',
          content: 'What are the latest developments in quantum computing?'
        }
      ]
    };

    console.log('Sending request to:', apiUrl);
    console.log('Payload:', JSON.stringify(testMessage, null, 2));

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testMessage),
    });

    const responseData = await response.json();

    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(responseData, null, 2));

    if (response.status === 200 && responseData.result) {
      console.log('✅ Legacy API test passed');
      return true;
    } else {
      console.log('❌ Legacy API test failed');
      return false;
    }

  } catch (error) {
    console.error('❌ Legacy API test error:', error);
    return false;
  }
}

/**
 * Test the LangGraph streaming API
 */
async function testLangGraphApi() {
  console.log('\n🔍 Testing LangGraph Streaming API...');

  try {
    // Test assistant endpoint
    const assistantUrl = 'http://localhost:2024/assistants/agent';
    console.log('Testing assistant endpoint:', assistantUrl);

    const assistantResponse = await fetch(assistantUrl);
    const assistantData = await assistantResponse.json();

    console.log('Assistant response:', JSON.stringify(assistantData, null, 2));

    if (assistantResponse.status !== 200) {
      console.log('❌ Assistant endpoint test failed');
      return false;
    }

    // Test thread creation
    const threadUrl = 'http://localhost:2024/threads';
    console.log('Creating thread at:', threadUrl);

    const threadResponse = await fetch(threadUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ metadata: {} }),
    });

    const threadData = await threadResponse.json();
    console.log('Thread created:', JSON.stringify(threadData, null, 2));

    if (threadResponse.status !== 200) {
      console.log('❌ Thread creation test failed');
      return false;
    }

    // Test streaming run
    const streamUrl = `http://localhost:2024/threads/${threadData.thread_id}/runs/stream`;
    console.log('Testing streaming at:', streamUrl);

    const streamPayload = {
      assistant_id: 'agent',
      input: {
        messages: [
          {
            content: 'What are the latest developments in quantum computing?',
            type: 'human'
          }
        ]
      },
      config: {
        configurable: {
          initial_search_query_count: 2,
          max_research_loops: 1,
          reasoning_model: 'gemini-2.5-pro-preview-05-06'
        }
      },
      stream_mode: ['values']
    };

    console.log('Stream payload:', JSON.stringify(streamPayload, null, 2));

    const streamResponse = await fetch(streamUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(streamPayload),
    });

    if (streamResponse.status !== 200) {
      console.log('❌ Streaming test failed with status:', streamResponse.status);
      const errorText = await streamResponse.text();
      console.log('Error response:', errorText);
      return false;
    }

    console.log('✅ LangGraph API endpoints test passed');
    return true;

  } catch (error) {
    console.error('❌ LangGraph API test error:', error);
    return false;
  }
}

/**
 * Test configuration and environment setup
 */
async function testConfiguration() {
  console.log('\n🔍 Testing Configuration...');

  try {
    const configUrl = 'http://localhost:2024/assistants/agent/graph';
    const response = await fetch(configUrl);
    const data = await response.json();

    console.log('Graph configuration:', JSON.stringify(data, null, 2));

    if (response.status === 200 && data.nodes && data.edges) {
      console.log('✅ Configuration test passed');
      return true;
    } else {
      console.log('❌ Configuration test failed');
      return false;
    }

  } catch (error) {
    console.error('❌ Configuration test error:', error);
    return false;
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🚀 Starting Comprehensive Backend Migration Tests');
  console.log('================================================');

  const results = {
    legacy: false,
    langgraph: false,
    config: false,
  };

  // Wait for server to be ready
  console.log('⏳ Waiting for server to start...');
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Run tests
  results.legacy = await testLegacyApi();
  results.langgraph = await testLangGraphApi();
  results.config = await testConfiguration();

  // Summary
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  console.log(`Legacy API: ${results.legacy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`LangGraph API: ${results.langgraph ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Configuration: ${results.config ? '✅ PASS' : '❌ FAIL'}`);

  const totalPassed = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  console.log(`\nOverall: ${totalPassed}/${totalTests} tests passed`);

  if (totalPassed === totalTests) {
    console.log('🎉 All tests passed! Migration is successful.');
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.');
  }

  return totalPassed === totalTests;
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  runAllTests().catch(console.error);
}
