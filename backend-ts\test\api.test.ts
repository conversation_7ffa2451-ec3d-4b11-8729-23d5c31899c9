// Simple test file without external dependencies
// Note: In a real implementation, you would use axios for HTTP requests
// and dotenv for environment variables

/**
 * Simple test script to verify the agent API endpoint
 */
async function testAgentApi() {
  try {
    console.log('Testing agent API endpoint...');
    
    // Define the API endpoint
    const apiUrl = 'http://localhost:3000/api/run';
    
    // Define a test message
    const testMessage = {
      messages: [
        {
          type: 'human',
          content: 'What are the latest developments in quantum computing?'
        }
      ]
    };
    
    console.log('Sending test message:', JSON.stringify(testMessage, null, 2));
    
    // Send the request using fetch API instead of axios
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testMessage),
    });
    
    // Parse the response
    const responseData = await response.json();
    
    // Log the response
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(responseData, null, 2));
    
    // Verify the response structure
    if (responseData.result && 
        responseData.result.messages && 
        Array.isArray(responseData.result.messages)) {
      console.log('✅ Test passed: Response structure is valid');
    } else {
      console.log('❌ Test failed: Invalid response structure');
    }
    
    // Verify that the response contains an AI message
    const aiMessages = responseData.result.messages.filter(
      (msg: any) => msg.type === 'ai'
    );
    
    if (aiMessages.length > 0) {
      console.log('✅ Test passed: Response contains AI message');
    } else {
      console.log('❌ Test failed: No AI message in response');
    }
    
  } catch (error) {
    console.error('Error testing agent API:', error);
  }
}

// Run the test
testAgentApi();
