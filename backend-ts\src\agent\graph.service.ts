import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { StateGraph, START, END, Send } from '@langchain/langgraph';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';

import { OverallStateAnnotation, OverallState, SourceInfo } from './state';
import { ConfigurationService } from './configuration.service';
import { SearchQueryListSchema, ReflectionSchema, SearchQueryList, Reflection } from './tools-and-schemas';
import { 
  getCurrentDate, 
  queryWriterInstructions, 
  webSearcherInstructions, 
  reflectionInstructions, 
  answerInstructions 
} from './prompts';
import { 
  getResearchTopic, 
  resolveUrls, 
  getCitations, 
  insertCitationMarkers 
} from './utils';

/**
 * Query generation state for intermediate processing
 */
interface QueryGenerationState {
  query_list: Array<{ query: string; rationale: string }>;
}

/**
 * Reflection state for intermediate processing
 */
interface ReflectionState {
  is_sufficient: boolean;
  knowledge_gap: string;
  follow_up_queries: string[];
  research_loop_count: number;
  number_of_ran_queries: number;
}

/**
 * Web search state for individual search operations
 */
interface WebSearchState {
  search_query: string;
  id: number;
}

/**
 * LangGraph service that implements the agent workflow.
 * This is the core implementation that replaces the Python graph.py
 */
@Injectable()
export class GraphService {
  private genaiClient: GoogleGenerativeAI;
  private graph: any;

  constructor(
    private configService: ConfigService,
    private configurationService: ConfigurationService,
  ) {
    // Disable LangSmith tracing completely
    process.env.LANGCHAIN_TRACING_V2 = 'false';
    process.env.LANGCHAIN_API_KEY = '';

    // Check for GEMINI_API_KEY
    const geminiApiKey = this.configService.get<string>('GEMINI_API_KEY');
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY is not set');
    }

    // Initialize Google Generative AI client
    this.genaiClient = new GoogleGenerativeAI(geminiApiKey);

    // Build the graph
    this.buildGraph();
  }

  /**
   * Build the LangGraph state graph
   */
  private buildGraph() {
    const builder = new StateGraph(OverallStateAnnotation)
      .addNode('generate_query', this.generateQuery.bind(this))
      .addNode('web_research', this.webResearch.bind(this))
      .addNode('reflection', this.reflection.bind(this))
      .addNode('finalize_answer', this.finalizeAnswer.bind(this))
      .addEdge(START, 'generate_query')
      .addConditionalEdges(
        'generate_query',
        this.continueToWebResearch.bind(this),
        ['web_research']
      )
      .addEdge('web_research', 'reflection')
      .addConditionalEdges(
        'reflection',
        this.evaluateResearch.bind(this),
        ['web_research', 'finalize_answer']
      )
      .addEdge('finalize_answer', END);

    this.graph = builder.compile();
  }

  /**
   * Get the compiled graph for external use
   */
  getGraph() {
    return this.graph;
  }

  /**
   * LangGraph node that generates search queries based on the user's question.
   * Equivalent to the Python generate_query function.
   */
  private async generateQuery(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    try {
      const configurable = ConfigurationService.fromRunnableConfig(config);

      // Check for custom initial search query count
      const initialSearchQueryCount = state.initial_search_query_count ?? configurable.numberOfInitialQueries;

      // Initialize Gemini model
      const llm = new ChatGoogleGenerativeAI({
        model: configurable.queryGeneratorModel,
        temperature: 1.0,
        maxRetries: 2,
        apiKey: this.configService.get<string>('GEMINI_API_KEY'),
      });

      // Format the prompt
      const currentDate = getCurrentDate();
      const formattedPrompt = queryWriterInstructions
        .replace('{current_date}', currentDate)
        .replace('{research_topic}', getResearchTopic(state.messages))
        .replace('{number_queries}', initialSearchQueryCount.toString());

      // Generate the search queries using structured output
      const structuredLlm = llm.withStructuredOutput(SearchQueryListSchema);
      const result = await structuredLlm.invoke(formattedPrompt);

      return {
        // Store the query list for the next step
        search_query: result.query,
      };
    } catch (error) {
      console.error('Error in generateQuery:', error);
      // Fallback to default queries if generation fails
      return {
        search_query: ['General information search'],
      };
    }
  }

  /**
   * LangGraph conditional edge that sends search queries to web research nodes.
   * Equivalent to the Python continue_to_web_research function.
   */
  private continueToWebResearch(state: OverallState): Send[] {
    return state.search_query.map((searchQuery, idx) => 
      new Send('web_research', { search_query: searchQuery, id: idx })
    );
  }

  /**
   * LangGraph node that performs web research using Google Search API.
   * Equivalent to the Python web_research function.
   */
  private async webResearch(
    state: WebSearchState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    
    // Format the prompt
    const formattedPrompt = webSearcherInstructions
      .replace('{current_date}', getCurrentDate())
      .replace('{research_topic}', state.search_query);

    // Use Google Generative AI client with search tool
    const model = this.genaiClient.getGenerativeModel({
      model: configurable.queryGeneratorModel,
      tools: [{ googleSearchRetrieval: {} }],
    });

    const response = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: formattedPrompt }] }],
      generationConfig: { temperature: 0 },
    });

    // Process the response and extract citations
    const responseText = response.response.text();
    const groundingMetadata = response.response.candidates?.[0]?.groundingMetadata;

    let resolvedUrls: Record<string, string> = {};
    let citations: any[] = [];
    let modifiedText = responseText;
    let sourcesGathered: SourceInfo[] = [];

    if (groundingMetadata?.groundingChuncks) {
      // Resolve URLs to short URLs for saving tokens
      resolvedUrls = resolveUrls(groundingMetadata.groundingChuncks, state.id);
      
      // Get citations and add them to the generated text
      citations = getCitations({ 
        candidates: [{ grounding_metadata: groundingMetadata }],
        text: responseText 
      }, resolvedUrls);
      
      modifiedText = insertCitationMarkers(responseText, citations);
      sourcesGathered = citations.flatMap(citation => citation.segments);
    }

    return {
      sources_gathered: sourcesGathered,
      search_query: [state.search_query],
      web_research_result: [modifiedText],
    };
  }

  /**
   * LangGraph node that identifies knowledge gaps and generates follow-up queries.
   * Equivalent to the Python reflection function.
   */
  private async reflection(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    const configurable = ConfigurationService.fromRunnableConfig(config);

    // Increment the research loop count
    const researchLoopCount = (state.research_loop_count ?? 0) + 1;
    const reasoningModel = state.reasoning_model ?? configurable.reflectionModel;

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = reflectionInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{summaries}', state.web_research_result.join('\n\n---\n\n'));

    // Initialize reasoning model
    const llm = new ChatGoogleGenerativeAI({
      model: reasoningModel,
      temperature: 1.0,
      maxRetries: 2,
      apiKey: this.configService.get<string>('GEMINI_API_KEY'),
    });

    const structuredLlm = llm.withStructuredOutput(ReflectionSchema);
    const result = await structuredLlm.invoke(formattedPrompt);

    // Store reflection data in the state for evaluation
    return {
      research_loop_count: researchLoopCount,
      // Store reflection results as metadata for evaluation
      _reflection_result: {
        is_sufficient: result.is_sufficient,
        knowledge_gap: result.knowledge_gap,
        follow_up_queries: result.follow_up_queries,
        number_of_ran_queries: state.search_query.length,
      },
    };
  }

  /**
   * LangGraph routing function that determines the next step in the research flow.
   * Equivalent to the Python evaluate_research function.
   */
  private evaluateResearch(
    state: OverallState,
    config?: RunnableConfig
  ): string | Send[] {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const maxResearchLoops = state.max_research_loops ?? configurable.maxResearchLoops;

    // Get reflection result from state metadata
    const reflectionResult = (state as any)._reflection_result;
    if (!reflectionResult) {
      return 'finalize_answer'; // Fallback if no reflection data
    }

    if (reflectionResult.is_sufficient || (state.research_loop_count ?? 0) >= maxResearchLoops) {
      return 'finalize_answer';
    } else {
      return reflectionResult.follow_up_queries.map((followUpQuery: string, idx: number) =>
        new Send('web_research', {
          search_query: followUpQuery,
          id: reflectionResult.number_of_ran_queries + idx,
        })
      );
    }
  }

  /**
   * LangGraph node that finalizes the research summary.
   * Equivalent to the Python finalize_answer function.
   */
  private async finalizeAnswer(
    state: OverallState,
    config?: RunnableConfig
  ): Promise<Partial<OverallState>> {
    const configurable = ConfigurationService.fromRunnableConfig(config);
    const reasoningModel = state.reasoning_model ?? configurable.answerModel;

    // Format the prompt
    const currentDate = getCurrentDate();
    const formattedPrompt = answerInstructions
      .replace('{current_date}', currentDate)
      .replace('{research_topic}', getResearchTopic(state.messages))
      .replace('{summaries}', state.web_research_result.join('\n---\n\n'));

    // Initialize reasoning model
    const llm = new ChatGoogleGenerativeAI({
      model: reasoningModel,
      temperature: 0,
      maxRetries: 2,
      apiKey: this.configService.get<string>('GEMINI_API_KEY'),
    });

    const result = await llm.invoke(formattedPrompt);

    // Replace short URLs with original URLs and collect used sources
    let content = result.content as string;
    const uniqueSources: SourceInfo[] = [];

    for (const source of state.sources_gathered) {
      if (content.includes(source.short_url)) {
        content = content.replace(source.short_url, source.value);
        uniqueSources.push(source);
      }
    }

    return {
      messages: [new AIMessage({ content })],
      sources_gathered: uniqueSources,
    };
  }
}
