import { Annotation } from '@langchain/langgraph';
import { BaseMessage } from '@langchain/core/messages';

/**
 * Source information for citations
 */
export interface SourceInfo {
  label: string;
  short_url: string;
  value: string;
}

/**
 * Overall state of the agent using LangGraph Annotation.
 * Equivalent to the Python OverallState TypedDict with proper reducers.
 */
export const OverallStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  search_query: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  web_research_result: Annotation<string[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  sources_gathered: Annotation<SourceInfo[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  initial_search_query_count: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 3,
  }),
  max_research_loops: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 2,
  }),
  research_loop_count: Annotation<number>({
    reducer: (x, y) => y ?? x,
    default: () => 0,
  }),
  reasoning_model: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => 'gemini-2.5-pro-preview-05-06',
  }),
  // Internal metadata for reflection results
  _reflection_result: Annotation<any>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),
});

export type OverallState = typeof OverallStateAnnotation.State;

/**
 * Reflection state of the agent.
 * Equivalent to the Python ReflectionState TypedDict.
 */
export interface ReflectionState {
  is_sufficient: boolean;
  knowledge_gap: string;
  follow_up_queries: string[];
  research_loop_count: number;
  number_of_ran_queries: number;
}

/**
 * Query generation state of the agent.
 * Equivalent to the Python QueryGenerationState TypedDict.
 */
export interface QueryGenerationState {
  query_list: Query[];
}

/**
 * Web search state of the agent.
 * Equivalent to the Python WebSearchState TypedDict.
 */
export interface WebSearchState {
  search_query: string;
  id: string;
}

/**
 * Query with rationale.
 * Equivalent to the Python Query TypedDict.
 */
export interface Query {
  query: string;
  rationale: string;
}

/**
 * Search state output.
 * Equivalent to the Python SearchStateOutput dataclass.
 */
export class SearchStateOutput {
  running_summary?: string;

  constructor(params: { running_summary?: string } = {}) {
    this.running_summary = params.running_summary;
  }
}
