import { AnyMessage } from './types';

/**
 * Overall state of the agent.
 * Equivalent to the Python OverallState TypedDict.
 */
export interface OverallState {
  messages: AnyMessage[];
  search_query: string[];
  web_research_result: string[];
  sources_gathered: any[];
  initial_search_query_count?: number;
  max_research_loops?: number;
  research_loop_count?: number;
  reasoning_model?: string;
}

/**
 * Reflection state of the agent.
 * Equivalent to the Python ReflectionState TypedDict.
 */
export interface ReflectionState {
  is_sufficient: boolean;
  knowledge_gap: string;
  follow_up_queries: string[];
  research_loop_count: number;
  number_of_ran_queries: number;
}

/**
 * Query generation state of the agent.
 * Equivalent to the Python QueryGenerationState TypedDict.
 */
export interface QueryGenerationState {
  query_list: Query[];
}

/**
 * Web search state of the agent.
 * Equivalent to the Python WebSearchState TypedDict.
 */
export interface WebSearchState {
  search_query: string;
  id: string;
}

/**
 * Query with rationale.
 * Equivalent to the Python Query TypedDict.
 */
export interface Query {
  query: string;
  rationale: string;
}

/**
 * Search state output.
 * Equivalent to the Python SearchStateOutput dataclass.
 */
export class SearchStateOutput {
  running_summary?: string;

  constructor(params: { running_summary?: string } = {}) {
    this.running_summary = params.running_summary;
  }
}
