/**
 * Search query list schema.
 * Equivalent to the Python SearchQueryList class.
 */
export class SearchQueryList {
  /**
   * A list of search queries to be used for web research.
   */
  query: string[];

  /**
   * A brief explanation of why these queries are relevant to the research topic.
   */
  rationale: string;

  constructor(params: { query: string[], rationale: string }) {
    this.query = params.query;
    this.rationale = params.rationale;
  }
}

/**
 * Reflection schema.
 * Equivalent to the Python Reflection class.
 */
export class Reflection {
  /**
   * Whether the provided summaries are sufficient to answer the user's question.
   */
  is_sufficient: boolean;

  /**
   * A description of what information is missing or needs clarification.
   */
  knowledge_gap: string;

  /**
   * A list of follow-up queries to address the knowledge gap.
   */
  follow_up_queries: string[];

  constructor(params: { is_sufficient: boolean, knowledge_gap: string, follow_up_queries: string[] }) {
    this.is_sufficient = params.is_sufficient;
    this.knowledge_gap = params.knowledge_gap;
    this.follow_up_queries = params.follow_up_queries;
  }
}
