/**
 * Base message interface
 */
export interface Message {
  content: string;
  type: string;
}

/**
 * Human message
 */
export interface HumanMessage extends Message {
  type: 'human';
}

/**
 * AI message
 */
export interface AIMessage extends Message {
  type: 'ai';
}

/**
 * Any message type
 */
export type AnyMessage = HumanMessage | AIMessage;

/**
 * Citation segment
 */
export interface CitationSegment {
  label: string;
  short_url: string;
  value: string;
}

/**
 * Citation
 */
export interface Citation {
  start_index: number;
  end_index: number;
  segments: CitationSegment[];
}

/**
 * Runnable configuration
 */
export interface RunnableConfig {
  configurable?: Record<string, any>;
}
