import { Controller, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { AgentService } from './agent.service';
import { AnyMessage } from './types';
import { OverallState } from './state';

/**
 * Controller for the agent API endpoints.
 */
@Controller('api')
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  /**
   * Endpoint to run the agent with a given set of messages.
   * This is the main entry point for the frontend to interact with the agent.
   */
  @Post('run')
  async runAgent(@Body() body: { messages: AnyMessage[] }): Promise<{ result: OverallState }> {
    try {
      // Validate input
      if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
        throw new HttpException('Invalid messages format', HttpStatus.BAD_REQUEST);
      }

      // Initialize the agent state
      const initialState: OverallState = {
        messages: body.messages,
        search_query: [],
        web_research_result: [],
        sources_gathered: [],
        initial_search_query_count: 3,
        max_research_loops: 2,
        research_loop_count: 0,
        reasoning_model: 'gemini-2.5-pro-preview-05-06',
      };

      // Run the agent
      const result = await this.agentService.runAgent(initialState);
      
      return { result };
    } catch (error) {
      // Handle errors
      const message = error instanceof Error ? error.message : 'An unknown error occurred';
      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
