/**
 * Simple test server startup script
 * This script starts the server and runs basic connectivity tests
 */

const { spawn } = require('child_process');
const fetch = require('node-fetch');

let serverProcess = null;

/**
 * Start the NestJS server
 */
function startServer() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting NestJS server...');
    
    serverProcess = spawn('npm', ['run', 'start:dev'], {
      cwd: __dirname,
      stdio: 'pipe'
    });

    let serverReady = false;

    serverProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('Server:', output.trim());
      
      if (output.includes('Application is running on') && !serverReady) {
        serverReady = true;
        console.log('✅ Server started successfully');
        resolve();
      }
    });

    serverProcess.stderr.on('data', (data) => {
      const error = data.toString();
      console.error('Server Error:', error.trim());
    });

    serverProcess.on('close', (code) => {
      console.log(`Server process exited with code ${code}`);
      if (!serverReady) {
        reject(new Error(`Server failed to start, exit code: ${code}`));
      }
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      if (!serverReady) {
        reject(new Error('Server startup timeout'));
      }
    }, 30000);
  });
}

/**
 * Test basic server connectivity
 */
async function testConnectivity() {
  console.log('\n🔍 Testing server connectivity...');
  
  try {
    // Test health endpoint
    const healthResponse = await fetch('http://localhost:2024/assistants/agent');
    console.log('Health check status:', healthResponse.status);
    
    if (healthResponse.status === 200) {
      console.log('✅ Server is responding correctly');
      return true;
    } else {
      console.log('❌ Server health check failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Connectivity test failed:', error.message);
    return false;
  }
}

/**
 * Cleanup function
 */
function cleanup() {
  if (serverProcess) {
    console.log('\n🛑 Stopping server...');
    serverProcess.kill('SIGTERM');
    serverProcess = null;
  }
}

/**
 * Main test function
 */
async function main() {
  try {
    // Start server
    await startServer();
    
    // Wait a bit for full initialization
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test connectivity
    const connectivityOk = await testConnectivity();
    
    if (connectivityOk) {
      console.log('\n🎉 Server is ready for testing!');
      console.log('You can now run the API tests or use the frontend.');
      console.log('\nTo test the API, run:');
      console.log('  node test/api.test.js');
      console.log('\nTo stop the server, press Ctrl+C');
      
      // Keep the server running
      process.on('SIGINT', () => {
        cleanup();
        process.exit(0);
      });
      
      process.on('SIGTERM', () => {
        cleanup();
        process.exit(0);
      });
      
    } else {
      console.log('❌ Server connectivity test failed');
      cleanup();
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    cleanup();
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  cleanup();
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  cleanup();
  process.exit(1);
});

// Run the main function
main();
